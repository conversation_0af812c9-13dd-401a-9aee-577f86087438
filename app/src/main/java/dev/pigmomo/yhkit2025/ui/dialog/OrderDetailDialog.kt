package dev.pigmomo.yhkit2025.ui.dialog

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Create
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.rememberAsyncImagePainter
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailPriceDetail
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailProduct
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailTimeSlot
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.DateUtils
import java.text.SimpleDateFormat
import java.util.Date
import androidx.core.net.toUri
import android.util.Log
import com.google.gson.GsonBuilder
import dev.pigmomo.yhkit2025.OrderDetailActivity

/**
 * 订单详情对话框
 */
@Composable
fun OrderDetailDialog(
    orderDetail: OrderDetailData,
    onDismiss: () -> Unit,
    onSaveProductConfig: (String) -> Unit,
    onOrderPrePay: (String, String) -> Unit,
    onUpdateDelivery: (String, String) -> Unit,
    onCancelOrder: (String) -> Unit,
    onRefundOrder: (String) -> Unit,
    onDeleteOrder: (String) -> Unit,
    onRedEnvelope: (String) -> Unit,
    onViewDeliveryPhotos: (List<String>) -> Unit
) {
    val context = LocalContext.current
    val statusmsg = orderDetail.statusmsg
    val statusInfoTitle = orderDetail.statusinfo?.title ?: ""
    val baseInfo = orderDetail.baseinfo
    val orderId = baseInfo?.id ?: ""
    val shopId = orderDetail.shopinfo?.shopId ?: ""
    val timeslots = orderDetail.timeslots
    val showTimeslots = timeslots?.show ?: false
    val timeslotsData = timeslots?.data ?: emptyList()

    // 添加状态变量来控制时间线对话框的显示
    var showTimeslotsDialog by remember { mutableStateOf(false) }

    // 如果显示时间线对话框，则渲染对话框
    if (showTimeslotsDialog) {
        TimeslotsDialog(
            timeslotsData = timeslotsData,
            onDismiss = { showTimeslotsDialog = false }
        )
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Row(
                    modifier = Modifier
                        .clip(RoundedCornerShape(6.dp))
                        .clickable {
                            if (showTimeslots) {
                                showTimeslotsDialog = true
                            }
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(statusmsg)
                    if (showTimeslots) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                            contentDescription = "查看订单时间线"
                        )
                    }
                }
                Text(text = statusInfoTitle, fontSize = 12.sp, lineHeight = 13.sp)
            }
        },
        text = {
            OrderDetailContent(
                orderDetail = orderDetail,
                onSaveProductConfig = onSaveProductConfig,
                onOrderPrePay = { payType -> onOrderPrePay(orderId, payType) },
                onUpdateDelivery = { onUpdateDelivery(orderId, shopId) },
                onCancelOrder = { onCancelOrder(orderId) },
                onRefundOrder = { onRefundOrder(orderId) },
                onDeleteOrder = { onDeleteOrder(orderId) },
                onRedEnvelope = { onRedEnvelope(orderId) },
                onViewDeliveryPhotos = onViewDeliveryPhotos
            )
        },
        confirmButton = { },
        dismissButton = { },
        containerColor = dialogContainerColor()
    )
}

/**
 * 订单时间线对话框
 * 展示订单的各个状态和对应的时间
 */
@Composable
fun TimeslotsDialog(
    timeslotsData: List<OrderDetailTimeSlot>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("订单跟踪") },
        text = {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                // 展示时间线
                timeslotsData.forEachIndexed { index, timeslot ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 左侧时间
                        Text(
                            text = timeslot.time,
                            fontSize = 14.sp,
                            modifier = Modifier.width(120.dp)
                        )

                        // 右侧状态名称
                        Text(
                            text = timeslot.name,
                            fontSize = 16.sp,
                            color = if (index == timeslotsData.size - 1) Color(0xFF4CAF50) else Color.Black
                        )
                    }

                    // 添加连接线，最后一项不需要
                    if (index < timeslotsData.size - 1) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Spacer(modifier = Modifier.width(60.dp))
                            Spacer(
                                modifier = Modifier
                                    .height(16.dp)
                                    .width(2.dp)
                                    .background(Color.LightGray)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = { },
        containerColor = dialogContainerColor()
    )
}

/**
 * 订单详情内容
 */
@SuppressLint("SimpleDateFormat")
@Composable
fun OrderDetailContent(
    orderDetail: OrderDetailData,
    onSaveProductConfig: (String) -> Unit,
    onOrderPrePay: (String) -> Unit,
    onUpdateDelivery: () -> Unit,
    onCancelOrder: () -> Unit,
    onRefundOrder: () -> Unit,
    onDeleteOrder: () -> Unit,
    onRedEnvelope: () -> Unit,
    onViewDeliveryPhotos: (List<String>) -> Unit
) {
    val context = LocalContext.current

    // 获取订单状态
    val orderStatus = orderDetail.status

    // 获取无接触配送照片
    val noTouchPics = orderDetail.noTouchPics
    val deliveryPhotos = noTouchPics?.pics ?: emptyList()

    // 获取商品列表
    val productsInfo = orderDetail.productsinfo
    val products = productsInfo?.products ?: emptyList()

    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
    ) {
        // 操作按钮卡片
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .padding(bottom = 2.dp)
                .fillMaxWidth()
        ) {
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                // 保存商品配置按钮
                TokenActionButton(
                    icon = R.drawable.baseline_save_24,
                    text = "商品配置",
                    onClick = {
                        val goods = buildProductConfigString(products)
                        onSaveProductConfig(goods)
                    }
                )

                // 待付款订单的支付按钮
                if (orderStatus == 1) {
                    TokenActionButton(
                        icon = R.drawable.baseline_payment_24,
                        text = "数字人名币",
                        onClick = { onOrderPrePay("pay.dcep.app.pay.and.sign") }
                    )

                    TokenActionButton(
                        icon = R.drawable.baseline_wallet_24,
                        text = "支付宝链接",
                        onClick = { onOrderPrePay("pay.alipay.app") }
                    )
                }

                // 待付款或待配送订单的修改和取消按钮
                if (orderStatus == 1 || orderStatus == 2) {
                    TokenActionButton(
                        imageVector = Icons.Filled.Create,
                        text = "修改订单",
                        onClick = { onUpdateDelivery() }
                    )

                    TokenActionButton(
                        imageVector = Icons.Filled.Close,
                        text = "取消订单",
                        onClick = {
                            if (orderStatus == 1) {
                                onCancelOrder()
                            } else {
                                onRefundOrder()
                            }
                        }
                    )
                }

                // 已完成、已取消或已退款订单的删除按钮
                if (orderStatus == 5 || orderStatus == 6 || orderStatus == 16) {
                    TokenActionButton(
                        imageVector = Icons.Filled.Delete,
                        text = "删除订单",
                        onClick = { onDeleteOrder() }
                    )
                }

                // 已完成、配送中、待配送订单的瓜分红包按钮
                if (orderStatus == 5 || orderStatus == 3 || orderStatus == 2) {
                    TokenActionButton(
                        icon = R.drawable.baseline_redeem_24,
                        text = "瓜分红包",
                        onClick = { onRedEnvelope() }
                    )
                }

                // 有送达照片的查看按钮
                if (deliveryPhotos.isNotEmpty()) {
                    TokenActionButton(
                        icon = R.drawable.baseline_assistant_photo_24,
                        text = "送达照片",
                        onClick = { onViewDeliveryPhotos(deliveryPhotos) }
                    )
                }

                // 跳转APP查看的按钮
                TokenActionButton(
                    icon = R.drawable.outline_pageview_24,
                    text = "订单详情",
                    onClick = {
                        // 跳转到OrderDetailActivity查看订单详情
                        try {
                            val intent = OrderDetailActivity.createIntent(context, orderDetail)
                            context.startActivity(intent)
                        } catch (e: Exception) {
                            Log.e("OrderDetailDialog", "Failed to start OrderDetailActivity", e)
                        }

                        // Hook 方案 废弃
                        // 序列化orderDetail为JSON字符串
                        //val gson = GsonBuilder().setPrettyPrinting().create()
                        //val orderDetailStr = gson.toJson(orderDetail)
                        // 储存orderId及其对应orderDetailStr

                        // 打开APP查看订单详情
                        /*val orderId = orderDetail.baseinfo?.id ?: ""
                        if (orderId.isNotEmpty()) {
                            val orderUrl =
                                "myyh://yhlife.com/show/native?name=orderdetail&orderid=$orderId"
                            val intent = Intent(Intent.ACTION_VIEW, orderUrl.toUri())
                            context.startActivity(intent)
                        }*/
                    }
                )
            }
        }

        // 只处理配送订单
        val orderType = orderDetail.ordertype
        if (orderType != 1) {
            return@Column
        }

        // 收货信息卡片
        if (orderStatus != 16) {
            val commentInfo = orderDetail.commentinfo
            val recvInfo = commentInfo?.recvinfo
            if (recvInfo != null) {
                val recvName = recvInfo.name
                val recvPhone = recvInfo.phone
                val recvAddress = recvInfo.address
                val recvAddressStr = if (recvAddress != null) {
                    "${recvAddress.area}${recvAddress.detail}"
                } else {
                    ""
                }

                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 2.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$recvName $recvPhone $recvAddressStr",
                        modifier = Modifier
                            .horizontalScroll(rememberScrollState())
                            .padding(10.dp)
                    )
                }
            }
        }

        // 订单信息卡片
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .padding(top = 2.dp)
                .fillMaxWidth()
        ) {
            Column {
                val baseInfo = orderDetail.baseinfo
                val commentInfo = orderDetail.commentinfo
                val orderPayInfo = orderDetail.orderpayinfo

                if (baseInfo != null) {
                    // 下单时间
                    val generateTime = baseInfo.generateTime
                    val formattedDate = DateUtils.formatDateTime(generateTime)

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            "下单时间",
                            modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                        )
                        Text(
                            formattedDate,
                            modifier = Modifier
                                .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                .horizontalScroll(rememberScrollState())
                        )
                    }

                    // 预约时间
                    val expectTime = commentInfo?.expecttime ?: ""
                    if (expectTime.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "预约时间",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                expectTime,
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }

                    // 配送员
                    val carrierName = commentInfo?.carriername ?: ""
                    if (carrierName.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "配送员",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                carrierName,
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }

                    // 订单编号
                    val id = baseInfo.id
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            "订单编号",
                            modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                        )
                        Text(
                            id,
                            modifier = Modifier
                                .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                .horizontalScroll(rememberScrollState())
                        )
                    }

                    // 支付方式
                    val payTypes = baseInfo.paytypes
                    if (payTypes.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "支付方式",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                payTypes,
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }

                    // 支付信息
                    if (orderPayInfo != null) {
                        val totalPayment = orderPayInfo.totalpayment / 100.0
                        val totalDiscount = orderPayInfo.totaldiscount / 100.0

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "支付信息",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                "优惠:￥${"%.2f".format(totalDiscount)} 实付:￥${
                                    "%.2f".format(
                                        totalPayment
                                    )
                                }",
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }

                    // 订单备注
                    val comment = baseInfo.comment
                    if (comment.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "订单备注",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                comment,
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }

                    // 缺货处理
                    val outOfStockMsg = baseInfo.outOfStockMsg
                    if (outOfStockMsg.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "缺货处理",
                                modifier = Modifier.padding(start = 10.dp, top = 5.dp)
                            )
                            Text(
                                outOfStockMsg,
                                modifier = Modifier
                                    .padding(start = 5.dp, end = 10.dp, top = 5.dp)
                                    .horizontalScroll(rememberScrollState())
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(5.dp))
            }
        }

        // 商品列表卡片
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .padding(vertical = 4.dp)
                .fillMaxWidth()
        ) {
            // 店铺名称
            val shopInfo = orderDetail.shopinfo
            val seller = shopInfo?.seller
            val shopName = seller?.title ?: ""

            Text(
                shopName,
                modifier = Modifier
                    .padding(start = 10.dp, top = 5.dp)
                    .horizontalScroll(rememberScrollState())
            )

            Spacer(modifier = Modifier.height(5.dp))

            // 商品列表
            Column {
                products.forEach { product ->
                    ProductItem(product)
                }

                // 价格详情
                val priceDetails = orderDetail.pricedetail
                priceDetails.forEach { priceDetail ->
                    PriceDetailItem(priceDetail)
                }

                Spacer(modifier = Modifier.height(5.dp))
            }
        }
    }
}

/**
 * 商品项
 */
@Composable
fun ProductItem(product: OrderDetailProduct) {
    val imgUrl = product.imgurl
    val price = product.price
    val showPrice = price?.total?.div(100.0) ?: 0.0
    val num = product.num / 100
    val unit = product.unit
    val actualPaidPrice = product.actualPaidPrice / 100.0
    val title = product.title
    val subTitle =
        "$num$unit 总价:￥${"%.2f".format(showPrice)} 实付:￥${"%.2f".format(actualPaidPrice)}"

    Row(
        modifier = Modifier.padding(vertical = 2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = rememberAsyncImagePainter(imgUrl),
            contentDescription = null,
            modifier = Modifier
                .padding(start = 10.dp)
                .width(40.dp)
                .height(40.dp)
                .clip(RoundedCornerShape(6.dp))
        )
        Column(
            modifier = Modifier.padding(start = 5.dp)
        ) {
            Text(
                title,
                fontSize = 13.sp,
                lineHeight = 15.sp,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState())
            )
            Text(
                text = subTitle,
                fontSize = 12.sp,
                lineHeight = 14.sp
            )
        }
    }
}

/**
 * 价格详情项
 */
@Composable
fun PriceDetailItem(priceDetail: OrderDetailPriceDetail) {
    val title = priceDetail.title
    val amount = priceDetail.amount

    val amountNumber = amount.replace("¥", "").toDouble()

    val amountColor = when {
        amount.startsWith("-") -> Color.Red
        title == "配送费" && amountNumber > 0 -> Color.Red
        title == "包装服务费" && amountNumber > 0 -> Color.Red
        else -> Color.Black
    }

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            title,
            modifier = Modifier.padding(start = 10.dp, top = 5.dp)
        )
        Text(
            amount,
            color = amountColor,
            modifier = Modifier.padding(end = 10.dp, top = 5.dp)
        )
    }
}

/**
 * 构建商品配置字符串
 */
private fun buildProductConfigString(products: List<OrderDetailProduct>): String {
    /**
     * 构建商品配置字符串
     * 将商品列表转换为特定格式的字符串，格式为: id,num,tagId,promoCode;
     * 需要兑换的商品(goodsTagId=3)会被放在列表末尾
     */
    return products
        .map { product ->
            val id = product.batchcode.ifEmpty { product.id }
            val num = product.num / 100
            val promoCode = product.bundlepromocode.ifEmpty { "NO-CODE" }
            "$id,$num,${product.goodstagid},$promoCode"
        }
        .sortedBy { it.split(",")[2] == "3" } // 将需要兑换的商品(goodsTagId=3)排到最后
        .joinToString(";") + ";"
} 