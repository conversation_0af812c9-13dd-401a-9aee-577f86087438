package dev.pigmomo.yhkit2025.ui.dialog

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.order.WidgetHelper
import dev.pigmomo.yhkit2025.api.model.order.AppointmentTimeDetail
import dev.pigmomo.yhkit2025.api.model.order.AppointmentTimeInfo
import dev.pigmomo.yhkit2025.api.model.order.AppointmentTimeResult
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceCoupon
import dev.pigmomo.yhkit2025.api.model.order.CouponsWidgetData
import dev.pigmomo.yhkit2025.api.model.order.DeliveryAmountWidgetData
import dev.pigmomo.yhkit2025.api.model.order.DiscountWidgetData
import dev.pigmomo.yhkit2025.api.model.order.FreeDeliveryWidgetData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceResponse
import dev.pigmomo.yhkit2025.api.model.order.PointsWidgetData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceProduct
import dev.pigmomo.yhkit2025.api.model.order.ProductsTotalAmountWidgetData
import dev.pigmomo.yhkit2025.api.model.order.RedPacketWidgetData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceRedPacket
import dev.pigmomo.yhkit2025.api.model.order.OrderPlacePackage
import dev.pigmomo.yhkit2025.api.model.order.ShoppingBagsDeliveryWidgetData
import dev.pigmomo.yhkit2025.api.model.order.ShoppingBagsPickSelfWidgetData
import dev.pigmomo.yhkit2025.api.model.order.SubtotalAmountWidgetData
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.DateUtils
import org.json.JSONArray
import org.json.JSONObject
import java.text.DecimalFormat

/**
 * 订单下单页面对话框
 * 显示订单详情，包括地址信息、配送时间、商品列表、价格详情和支付方式等
 *
 * @param orderPlaceResponse 订单下单响应数据
 * @param onDismiss 对话框关闭回调
 * @param onConfirmClick 支付按钮点击回调，传递orderPlaceBody参数
 */
@Composable
fun OrderPlaceDialog(
    orderPlaceResponse: OrderPlaceResponse,
    onDismiss: () -> Unit = {},
    onConfirmClick: (OrderPlaceData, String, String) -> Unit = { _, _, _ -> },
    onOrderCommentAction: (String) -> Unit = {},
    //selectedCouponCode、selectedRedPacketCode、balancePayOption、pointPayOption、pickSelf
    onOrderPlaceOptionsChanged: (String, String, Int, Int, Int) -> Unit = { _, _, _, _, _ -> },
    //selectedCoupon、selectedRedPacket、balancePayOption、pointPayOption、pickSelf、expectTime
    onSaveOrderPlaceConfig: (OrderPlaceCoupon?, OrderPlaceRedPacket?, Int, Int, Int, String, String, String) -> Unit = { _, _, _, _, _, _, _, _ -> },
    // 已保存的配送时间、订单备注
    expectTime: String = "",
    orderComment: String = ""
) {
    // 获取订单数据，如果为null则直接返回不显示对话框
    val orderPlaceData = orderPlaceResponse.data ?: return

    var selectedCouponCodeArr by remember { mutableStateOf("") }
    var selectedRedPacketCodeArr by remember { mutableStateOf("") }
    var balancePayOption by remember { mutableIntStateOf(orderPlaceData.balancepayoption) }
    var pointPayOption by remember { mutableIntStateOf(orderPlaceData.pointpayoption) }
    var pickSelf by remember { mutableIntStateOf(orderPlaceData.ispickself) }
    var expectTime by remember { mutableStateOf(expectTime) }

    // 控制订单备注对话框的显示状态
    var showOrderCommentDialog by remember { mutableStateOf(false) }
    var tempOrderComment by remember { mutableStateOf(orderComment) }

    val selectedOrderPlaceCoupon = remember { mutableStateOf<OrderPlaceCoupon?>(null) }
    val selectedOrderPlaceRedPacket = remember { mutableStateOf<OrderPlaceRedPacket?>(null) }

    val productsTotalAmount = remember { mutableStateOf("") }
    val totalPaymentNew =
        remember(orderPlaceData.totalpaymentNew) { mutableStateOf(orderPlaceData.totalpaymentNew) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                orderPlaceData.title,
                modifier = Modifier.horizontalScroll(rememberScrollState())
            )
        },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                // 地址操作按钮卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {

                        // 订单备注
                        TokenActionButton(
                            imageVector = Icons.Filled.Edit,
                            text = "订单备注",
                            onClick = {
                                if (tempOrderComment.isEmpty()) {
                                    showOrderCommentDialog = true
                                } else {
                                    tempOrderComment = ""
                                }
                            },
                            tint = if (tempOrderComment.isNotEmpty()) Color(0xFF48454E) else Color(
                                0xFF48454E
                            ).copy(alpha = 0.5f)
                        )

                        // 自提
                        TokenActionButton(
                            imageVector = Icons.Filled.Favorite,
                            text = "自提",
                            onClick = {
                                pickSelf = if (pickSelf == 1) 0 else 1
                                onOrderPlaceOptionsChanged(
                                    selectedCouponCodeArr,
                                    selectedRedPacketCodeArr,
                                    balancePayOption,
                                    pointPayOption,
                                    pickSelf
                                )
                            },
                            tint = if (pickSelf == 1) Color(0xFF48454E) else Color(0xFF48454E).copy(
                                alpha = 0.5f
                            )
                        )

                        // 保存配置按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "订单配置",
                            onClick = {
                                onSaveOrderPlaceConfig(
                                    selectedOrderPlaceCoupon.value,
                                    selectedOrderPlaceRedPacket.value,
                                    balancePayOption,
                                    pointPayOption,
                                    pickSelf,
                                    expectTime,
                                    productsTotalAmount.value,
                                    totalPaymentNew.value
                                )
                            }
                        )
                    }
                }

                // 订单内容滚动区域
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    // 地址信息部分
                    item {
                        AddressSection(orderPlaceData, orderPlaceData.ispickself)
                    }

                    // 配送时间部分
                    item {
                        Card(
                            colors = cardThemeOverlay(),
                            modifier = Modifier
                                .padding(top = 4.dp, bottom = 4.dp)
                                .fillMaxWidth()
                        ) {
                            DeliveryTimeSection(expectTime, orderPlaceData, onExpectTime = {
                                expectTime = it
                            })
                            ProductsSection(orderPlaceData)
                        }
                    }

                    // 价格详情部分，包括优惠券、红包、积分、配送券选择
                    item {
                        PriceInfoSection(
                            orderPlaceData,
                            productsTotalAmount,
                            onInitOrSync = { coupon, redPacket ->
                                if (coupon != null) {
                                    selectedCouponCodeArr = """["${coupon.code}"]"""
                                    selectedOrderPlaceCoupon.value = coupon
                                }
                                if (redPacket != null) {
                                    selectedRedPacketCodeArr = """["${redPacket.code}"]"""
                                    selectedOrderPlaceRedPacket.value = redPacket
                                }
                            },
                            onSelectedCoupon = {
                                selectedCouponCodeArr = if (it == null) {
                                    selectedOrderPlaceCoupon.value = null
                                    ""
                                } else {
                                    selectedOrderPlaceCoupon.value = it
                                    """["${it.code}"]"""
                                }

                                onOrderPlaceOptionsChanged(
                                    selectedCouponCodeArr,
                                    selectedRedPacketCodeArr,
                                    balancePayOption,
                                    pointPayOption,
                                    pickSelf
                                )
                            },
                            onSelectedRedPacket = {
                                selectedRedPacketCodeArr = if (it == null) {
                                    selectedOrderPlaceRedPacket.value = null
                                    ""
                                } else {
                                    selectedOrderPlaceRedPacket.value = it
                                    """["${it.code}"]"""
                                }

                                onOrderPlaceOptionsChanged(
                                    selectedCouponCodeArr,
                                    selectedRedPacketCodeArr,
                                    balancePayOption,
                                    pointPayOption,
                                    pickSelf
                                )
                            },
                            onPointPayOption = {
                                pointPayOption = if (it == 1) 0 else 1
                                onOrderPlaceOptionsChanged(
                                    selectedCouponCodeArr,
                                    selectedRedPacketCodeArr,
                                    balancePayOption,
                                    pointPayOption,
                                    pickSelf
                                )
                            }
                        )
                    }

                    item {
                        RemarkSection(tempOrderComment, onRemarkClick = {
                            if (it.isEmpty()) {
                                tempOrderComment = orderComment
                                showOrderCommentDialog = true
                            } else {
                                tempOrderComment = ""
                            }
                        })
                    }

                    // 余额支付方式部分
                    item {
                        PaymentMethodSection(orderPlaceData, onBalancePayOption = {
                            balancePayOption = if (it == 1) 0 else 1
                            onOrderPlaceOptionsChanged(
                                selectedCouponCodeArr,
                                selectedRedPacketCodeArr,
                                balancePayOption,
                                pointPayOption,
                                pickSelf
                            )
                        })
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = { onConfirmClick(orderPlaceData, tempOrderComment, expectTime) }) {
                Text("支付${orderPlaceData.totalpaymentNew}")
            }
        },
        dismissButton = {
            Button(onClick = { onDismiss() }) {
                Text("取消")
            }
        }
    )

    // 订单备注输入对话框
    if (showOrderCommentDialog) {
        AlertDialog(
            onDismissRequest = { showOrderCommentDialog = false },
            title = { Text("订单备注") },
            containerColor = dialogContainerColor(),
            text = {
                OutlinedTextField(
                    value = tempOrderComment,
                    onValueChange = { tempOrderComment = it },
                    label = { Text("订单备注") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            },
            confirmButton = {
                Button(onClick = {
                    onOrderCommentAction(tempOrderComment)
                    showOrderCommentDialog = false
                }) {
                    Text("确认")
                }
            },
            dismissButton = {
                Button(onClick = {
                    tempOrderComment = ""
                    showOrderCommentDialog = false
                }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 地址信息部分
 * 显示收件人姓名、电话和详细地址
 */
@Composable
private fun AddressSection(orderData: OrderPlaceData, pickSelf: Int) {
    // 使用WidgetHelper获取地址小部件数据
    val addressData = WidgetHelper.getAddressData(orderData)

    // 如果地址数据存在且包含接收信息，则显示地址卡片
    if (addressData != null) {
        val showAddress = if (pickSelf == 1) {
            val pickSelfData = addressData.pickself
            (pickSelfData?.shopname ?: "") + " " + (pickSelfData?.shopaddr ?: "")
        } else {
            val address = addressData.recvinfo?.address
            (addressData.recvinfo?.name ?: "") + " " + (addressData.recvinfo?.phone
                ?: "") + " " + address?.area + address?.detail
        }

        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Text(
                text = showAddress,
                fontSize = 14.sp,
                lineHeight = 18.sp,
                modifier = Modifier
                    .padding(8.dp)
                    .horizontalScroll(rememberScrollState())
            )
        }
    }
}

/**
 * 配送时间部分
 * 显示包裹名称和预约配送时间，支持嵌套下拉菜单选择配送日期和时间
 * 同时支持普通配送和预售配送两种模式
 */
@Composable
private fun DeliveryTimeSection(
    expectTime: String,
    orderData: OrderPlaceData,
    onExpectTime: (String) -> Unit = {}
) {
    // 使用WidgetHelper获取包裹小部件数据
    val packagesData = WidgetHelper.getPackagesData(orderData)
    val packages = packagesData?.packages ?: return

    /**
     * 构建配送时间JSON字符串 - 普通配送模式
     *
     * @param dateResult 日期结果信息
     * @param timeInfo 时间段信息
     * @return 配送时间的JSON字符串
     */
    fun buildExpectTimeJson(
        dateResult: AppointmentTimeResult,
        timeInfo: AppointmentTimeInfo
    ): String {
        val jsonObject = JSONObject()
        jsonObject.put("date", dateResult.appointmentDate)

        val timeslotsArray = JSONArray()
        val timeslotObject = JSONObject()

        // 设置时间槽属性
        timeslotObject.put("displayMode", 0)

        // 判断是否为立即配送
        if (timeInfo.immediateSupport == 1) {
            timeslotObject.put("immediatedesc", timeInfo.timeRow)
            timeslotObject.put("slottype", "immediate")
        } else {
            timeslotObject.put("from", timeInfo.start)
            timeslotObject.put("to", timeInfo.end)
            timeslotObject.put("slottype", "expectTime")
        }

        timeslotsArray.put(timeslotObject)
        jsonObject.put("timeslots", timeslotsArray)

        return jsonObject.toString()
    }

    /**
     * 构建预售配送时间JSON字符串
     *
     * @param selectedDate 选择的日期时间戳
     * @param timeDetail 时间详情
     * @param displayMode 显示模式
     * @return 预售配送时间的JSON字符串
     */
    @SuppressLint("DefaultLocale")
    fun buildPreSaleExpectTimeJson(
        selectedDate: Long,
        timeDetail: AppointmentTimeDetail,
        displayMode: Int
    ): String {
        val jsonObject = JSONObject()
        jsonObject.put("date", selectedDate)

        val timeslotsArray = JSONArray()
        val timeslotObject = JSONObject()

        // 设置时间槽属性
        timeslotObject.put("displayMode", displayMode)

        // 判断是否为立即配送
        if (timeDetail.isimmediatesupport == 1) {
            timeslotObject.put("immediatedesc", timeDetail.immediatedescription)
            timeslotObject.put("slottype", "immediate")
        } else {
            // 格式化开始和结束时间
            val from = String.format("%02d:%02d", timeDetail.fromhour, timeDetail.fromminute)
            val to = String.format("%02d:%02d", timeDetail.tohour, timeDetail.tominute)

            timeslotObject.put("from", from)
            timeslotObject.put("to", to)
            timeslotObject.put("slottype", "expectTime")
        }

        timeslotsArray.put(timeslotObject)
        jsonObject.put("timeslots", timeslotsArray)

        return jsonObject.toString()
    }

    // 如果有包裹，显示第一个包裹的配送时间
    if (packages.isNotEmpty()) {
        val firstPackage = packages[0]

        // 检查是否为预售模式
        val isPreSaleMode = firstPackage.appointment?.appointments?.isNotEmpty() == true

        if (isPreSaleMode) {
            // 预售模式时间选择
            PreSaleDeliveryTimeSection(
                expectTime = expectTime,
                firstOrderPlacePackage = firstPackage,
                onExpectTime = onExpectTime,
                buildPreSaleExpectTimeJson = { selectedDate, timeDetail, displayMode ->
                    buildPreSaleExpectTimeJson(selectedDate, timeDetail, displayMode)
                }
            )
        } else {
            // 普通模式时间选择
            NormalDeliveryTimeSection(
                expectTime = expectTime,
                firstOrderPlacePackage = firstPackage,
                onExpectTime = onExpectTime,
                buildExpectTimeJson = { dateResult, timeInfo ->
                    buildExpectTimeJson(dateResult, timeInfo)
                }
            )
        }
    }
}

/**
 * 普通配送时间选择部分
 */
@Composable
private fun NormalDeliveryTimeSection(
    expectTime: String,
    firstOrderPlacePackage: OrderPlacePackage,
    onExpectTime: (String) -> Unit,
    buildExpectTimeJson: (AppointmentTimeResult, AppointmentTimeInfo) -> String
) {
    // 当前选中的配送时间文本
    val currentTimeText = remember {
        mutableStateOf(
            if (expectTime.isNotEmpty()) {
                val jsonObject = JSONObject(expectTime)
                // 格式化时间戳，格式为今天、明天或具体日期
                val date = DateUtils.formatDateRelative(jsonObject.getLong("date"))
                val timeslots = jsonObject.getJSONArray("timeslots")
                val timeInfo = timeslots.getJSONObject(0)
                val slottype = timeInfo.getString("slottype")
                when (slottype) {
                    "immediate" -> timeInfo.getString("immediatedesc")
                    "expectTime" -> {
                        val from = timeInfo.getString("from")
                        val to = timeInfo.getString("to")
                        "$date ${from}-${to}"
                    }

                    else -> ""
                }
            } else {
                if (firstOrderPlacePackage.appointmentTimeResults.isNotEmpty() &&
                    firstOrderPlacePackage.appointmentTimeResults[0].appointmentTimeInfos.isNotEmpty()
                ) {
                    val timeInfo =
                        firstOrderPlacePackage.appointmentTimeResults[0].appointmentTimeInfos.find { it.isAvailed == 1 }
                    if (timeInfo != null) {
                        if (timeInfo.timeRow.contains("尽快送达"))
                            timeInfo.timeRow
                        else
                            "${firstOrderPlacePackage.appointmentTimeResults[0].dateRow} ${timeInfo.timeRow}"
                    } else ""
                } else ""
            }
        )
    }

    // 初始化expectTime - 自动选择第一个可用的配送时间
    remember {
        if (expectTime.isEmpty() && firstOrderPlacePackage.appointmentTimeResults.isNotEmpty()) {
            val firstDateResult = firstOrderPlacePackage.appointmentTimeResults[0]
            val firstAvailableTimeInfo =
                firstDateResult.appointmentTimeInfos.find { it.isAvailed == 1 }

            if (firstAvailableTimeInfo != null) {
                // 使用提取的函数构建JSON并设置初始值
                val jsonString = buildExpectTimeJson(firstDateResult, firstAvailableTimeInfo)
                onExpectTime(jsonString)
            }
        }
        true
    }

    // 控制日期下拉菜单的展开状态
    val dateDropdownExpanded = remember { mutableStateOf(false) }

    // 控制时间下拉菜单的展开状态和所选日期索引
    val timeDropdownExpanded = remember { mutableStateOf(false) }
    val selectedDateIndex = remember { mutableIntStateOf(0) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 8.dp, end = 8.dp, top = 4.dp, bottom = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 包裹全名
        Text(
            text = firstOrderPlacePackage.packagefullname,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f)
        )

        // 配送时间选择器
        if (firstOrderPlacePackage.appointmentTimeResults.isNotEmpty()) {
            Box {
                // 显示当前选中的配送时间，点击后展开日期下拉菜单
                Row(
                    modifier = Modifier
                        .clip(RoundedCornerShape(4.dp))
                        .clickable { dateDropdownExpanded.value = true }
                        .padding(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = currentTimeText.value.ifEmpty { "选择配送时间" },
                        fontSize = 14.sp,
                    )
                }

                // 日期选择下拉菜单
                DropdownMenu(
                    expanded = dateDropdownExpanded.value,
                    onDismissRequest = { dateDropdownExpanded.value = false }
                ) {
                    firstOrderPlacePackage.appointmentTimeResults.forEachIndexed { index, dateResult ->
                        DropdownMenuItem(
                            text = { Text(dateResult.dateRow) },
                            onClick = {
                                selectedDateIndex.intValue = index
                                timeDropdownExpanded.value = true
                            }
                        )
                    }
                }

                // 时间选择下拉菜单
                if (selectedDateIndex.intValue >= 0 && selectedDateIndex.intValue < firstOrderPlacePackage.appointmentTimeResults.size) {
                    val selectedDate =
                        firstOrderPlacePackage.appointmentTimeResults[selectedDateIndex.intValue]

                    DropdownMenu(
                        expanded = timeDropdownExpanded.value,
                        onDismissRequest = { timeDropdownExpanded.value = false }
                    ) {
                        selectedDate.appointmentTimeInfos.forEach { timeInfo ->
                            DropdownMenuItem(
                                text = { Text(timeInfo.timeRow) },
                                onClick = {
                                    // 更新选中的时间文本
                                    currentTimeText.value =
                                        if (timeInfo.timeRow.contains("尽快送达")) {
                                            timeInfo.timeRow
                                        } else {
                                            "${selectedDate.dateRow} ${timeInfo.timeRow}"
                                        }

                                    // 使用提取的函数构建JSON
                                    val jsonString = buildExpectTimeJson(selectedDate, timeInfo)
                                    onExpectTime(jsonString)

                                    // 关闭下拉菜单
                                    timeDropdownExpanded.value = false
                                    dateDropdownExpanded.value = false
                                },
                                enabled = timeInfo.isAvailed == 1
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 预售配送时间选择部分
 */
@SuppressLint("DefaultLocale")
@Composable
private fun PreSaleDeliveryTimeSection(
    expectTime: String,
    firstOrderPlacePackage: OrderPlacePackage,
    onExpectTime: (String) -> Unit,
    buildPreSaleExpectTimeJson: (Long, AppointmentTimeDetail, Int) -> String
) {
    // 获取预售配送信息
    val appointmentInfo = firstOrderPlacePackage.appointment?.appointments?.firstOrNull() ?: return

    // 控制日期下拉菜单的展开状态
    val dateDropdownExpanded = remember { mutableStateOf(false) }

    // 控制时间下拉菜单的展开状态
    val timeDropdownExpanded = remember { mutableStateOf(false) }

    // 记录选中的日期索引和时间戳
    val selectedDateIndex = remember { mutableIntStateOf(0) }
    val selectedDate = remember { mutableLongStateOf(appointmentInfo.dates.firstOrNull() ?: 0L) }

    // 格式化日期
    val formatDate = { timestamp: Long ->
        DateUtils.formatDateWithWeekday(timestamp)
    }

    // 生成时间选项列表
    val generateTimeOptions = { timeDetail: AppointmentTimeDetail ->
        val options = mutableListOf<Pair<String, Pair<Int, Int>>>()

        if (timeDetail.isimmediatesupport == 1) {
            // 立即配送选项
            options.add(Pair(timeDetail.immediatedescription, Pair(-1, -1)))
        } else {
            // 生成时间段选项
            val startHour = timeDetail.fromhour
            val endHour = timeDetail.tohour
            val interval = timeDetail.interval.coerceAtLeast(30) // 确保间隔至少为30分钟

            var currentHour = startHour
            var currentMinute = timeDetail.fromminute

            while (currentHour < endHour || (currentHour == endHour && currentMinute <= timeDetail.tominute)) {
                val nextMinute = (currentMinute + interval) % 60
                val nextHour = currentHour + (currentMinute + interval) / 60

                if (nextHour > endHour || (nextHour == endHour && nextMinute > timeDetail.tominute)) {
                    break
                }

                val timeText = String.format(
                    "%02d:%02d-%02d:%02d",
                    currentHour,
                    currentMinute,
                    nextHour,
                    nextMinute
                )

                options.add(Pair(timeText, Pair(currentHour, currentMinute)))

                currentHour = nextHour
                currentMinute = nextMinute
            }
        }

        options
    }

    // 当前选中的配送时间文本
    val currentTimeText = remember {
        mutableStateOf(
            if (expectTime.isNotEmpty()) {
                val jsonObject = JSONObject(expectTime)
                // 格式化时间戳，格式为具体日期
                val date = formatDate(jsonObject.getLong("date"))
                val timeslots = jsonObject.getJSONArray("timeslots")
                val timeInfo = timeslots.getJSONObject(0)
                val slottype = timeInfo.getString("slottype")
                when (slottype) {
                    "immediate" -> timeInfo.getString("immediatedesc")
                    "expectTime" -> {
                        val from = timeInfo.getString("from")
                        val to = timeInfo.getString("to")
                        "$date ${from}-${to}"
                    }
                    else -> ""
                }
            } else if (appointmentInfo.dates.isNotEmpty() && appointmentInfo.times.isNotEmpty()) {
                val firstDate = appointmentInfo.dates[0]
                val firstTime = appointmentInfo.times[0]

                if (firstTime.isimmediatesupport == 1) {
                    firstTime.immediatedescription
                } else {
                    val from = String.format("%02d:%02d", firstTime.fromhour, firstTime.fromminute)
                    val to = String.format("%02d:%02d", firstTime.tohour, firstTime.tominute)
                    "${formatDate(firstDate)} ${from}-${to}"
                }
            } else ""
        )
    }

    // 初始化expectTime - 如果为空则自动选择第一个可用的预售配送时间
    remember {
        if (expectTime.isEmpty() && appointmentInfo.dates.isNotEmpty() && appointmentInfo.times.isNotEmpty()) {
            val firstDate = appointmentInfo.dates[0]
            val firstTime = appointmentInfo.times[0]

            // 使用提取的函数构建JSON并设置初始值
            val jsonString = buildPreSaleExpectTimeJson(
                firstDate,
                firstTime,
                appointmentInfo.displayMode
            )
            onExpectTime(jsonString)
        } else if (expectTime.isNotEmpty()) {
            // 如果已有expectTime值，则解析并设置相关状态
            try {
                val jsonObject = JSONObject(expectTime)
                val date = jsonObject.getLong("date")
                
                // 找到对应的日期索引
                val dateIndex = appointmentInfo.dates.indexOfFirst { it == date }
                if (dateIndex >= 0) {
                    selectedDateIndex.intValue = dateIndex
                    selectedDate.longValue = date
                }
            } catch (e: Exception) {
                // 解析失败时使用默认值
                if (appointmentInfo.dates.isNotEmpty()) {
                    selectedDate.longValue = appointmentInfo.dates[0]
                }
            }
        }
        true
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 8.dp, end = 8.dp, top = 4.dp, bottom = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 包裹全名
        Text(
            text = firstOrderPlacePackage.packagefullname,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f)
        )

        // 预售配送时间选择器
        if (appointmentInfo.dates.isNotEmpty()) {
            Box {
                // 显示当前选中的预售配送时间，点击后展开日期下拉菜单
                Row(
                    modifier = Modifier
                        .clip(RoundedCornerShape(4.dp))
                        .clickable { dateDropdownExpanded.value = true }
                        .padding(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = currentTimeText.value.ifEmpty { "选择配送时间" },
                        fontSize = 14.sp,
                    )
                }

                // 日期选择下拉菜单
                DropdownMenu(
                    expanded = dateDropdownExpanded.value,
                    onDismissRequest = { dateDropdownExpanded.value = false }
                ) {
                    appointmentInfo.dates.forEachIndexed { index, dateTimestamp ->
                        DropdownMenuItem(
                            text = { Text(formatDate(dateTimestamp)) },
                            onClick = {
                                selectedDate.longValue = dateTimestamp
                                selectedDateIndex.intValue = index
                                timeDropdownExpanded.value = true
                                dateDropdownExpanded.value = false
                            }
                        )
                    }
                }

                // 时间选项下拉菜单
                if (timeDropdownExpanded.value && appointmentInfo.times.isNotEmpty()) {
                    val timeDetail = appointmentInfo.times.firstOrNull() ?: return@Box
                    val timeOptions = generateTimeOptions(timeDetail)

                    DropdownMenu(
                        expanded = timeDropdownExpanded.value,
                        onDismissRequest = { timeDropdownExpanded.value = false }
                    ) {
                        timeOptions.forEach { (timeText, hourMinute) ->
                            DropdownMenuItem(
                                text = { Text(timeText) },
                                onClick = {
                                    // 更新选中的预售时间文本
                                    currentTimeText.value =
                                        if (timeDetail.isimmediatesupport == 1) {
                                            timeText
                                        } else {
                                            "${formatDate(selectedDate.longValue)} $timeText"
                                        }

                                    // 创建一个修改过的时间详情，包含选择的时间
                                    val selectedTimeDetail = if (hourMinute.first == -1) {
                                        // 立即配送选项
                                        timeDetail
                                    } else {
                                        // 根据选择的时间创建新的时间详情
                                        val (hour, minute) = hourMinute
                                        val endMinute = (minute + timeDetail.interval) % 60
                                        val endHour = hour + (minute + timeDetail.interval) / 60

                                        AppointmentTimeDetail(
                                            isimmediatesupport = 0,
                                            fromhour = hour,
                                            fromminute = minute,
                                            tohour = endHour,
                                            tominute = endMinute,
                                            interval = timeDetail.interval,
                                            immediatedescription = ""
                                        )
                                    }

                                    // 构建JSON并回调
                                    val jsonString = buildPreSaleExpectTimeJson(
                                        selectedDate.longValue,
                                        selectedTimeDetail,
                                        appointmentInfo.displayMode
                                    )
                                    onExpectTime(jsonString)

                                    // 关闭下拉菜单
                                    timeDropdownExpanded.value = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 商品列表部分
 * 显示所有商品的信息，包括图片、标题、规格、价格和数量
 */
@Composable
private fun ProductsSection(orderData: OrderPlaceData) {
    val products =
        WidgetHelper.getPackagesData(orderData)?.packages?.flatMap { it.products } ?: return
    Row(
        modifier = Modifier
            .padding(start = 8.dp, end = 8.dp, top = 4.dp, bottom = 8.dp)
            .horizontalScroll(rememberScrollState())
    ) {
        products.forEachIndexed { _, product ->
            ProductItem(product)
        }
    }
}

/**
 * 单个商品项
 * 显示商品图片、标题、规格、价格和数量
 * 对于预售商品，显示预售标签
 */
@Composable
private fun ProductItem(orderPlaceProduct: OrderPlaceProduct) {
    val context = LocalContext.current
    val priceFormatter = remember { DecimalFormat("¥#,##0.00") }

    // 计算实际价格（分转元）
    val actualPrice = remember(orderPlaceProduct) {
        orderPlaceProduct.price?.total?.toDouble()?.div(100) ?: 0.0
    }

    Box(modifier = Modifier.padding(end = 4.dp)) {
        // 商品图片
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(orderPlaceProduct.imgurl)
                .crossfade(true)
                .build(),
            contentDescription = orderPlaceProduct.title,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(4.dp))
        )

        // 数量标签
        Text(
            text = " x${orderPlaceProduct.num / 100} ",
            color = Color.White,
            fontSize = 10.sp,
            lineHeight = 10.sp,
            modifier = Modifier
                .offset(x = 2.dp, y = (-2).dp)
                .align(Alignment.TopEnd)
                .background(
                    color = Color(0x80000000),
                    shape = RoundedCornerShape(6.dp)
                )
        )

        // 价格标签
        Text(
            text = " ${priceFormatter.format(actualPrice)} ",
            color = Color.White,
            fontSize = 10.sp,
            lineHeight = 10.sp,
            modifier = Modifier
                .offset(x = 2.dp, y = 2.dp)
                .align(Alignment.BottomEnd)
                .background(
                    color = Color(0xFFFF3B30),
                    shape = RoundedCornerShape(6.dp)
                )
        )
    }
}

/**
 * 价格部分
 * 价格详情部分，包括优惠券、红包、积分、配送券选择
 */
@Composable
private fun PriceInfoSection(
    orderData: OrderPlaceData,
    productsTotalAmount: MutableState<String>,
    onInitOrSync: (OrderPlaceCoupon?, OrderPlaceRedPacket?) -> Unit = { _, _ -> },
    onSelectedCoupon: (OrderPlaceCoupon?) -> Unit = {},
    onSelectedRedPacket: (OrderPlaceRedPacket?) -> Unit = {},
    onPointPayOption: (Int) -> Unit = {}
) {
    val priceInfo = WidgetHelper.getPriceInfoGroupData(orderData) ?: return
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .padding(bottom = 4.dp)
            .fillMaxWidth()
    ) {
        Column(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 8.dp)) {
            // 商品金额
            val productsTotalAmountWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<ProductsTotalAmountWidgetData>(
                    priceInfo,
                    "productstotalamount"
                )
            if (productsTotalAmountWidgetData != null) {
                productsTotalAmount.value = productsTotalAmountWidgetData.desc ?: ""
                PriceItem(
                    label = productsTotalAmountWidgetData.title ?: "",
                    value = productsTotalAmountWidgetData.desc ?: ""
                )
            }

            // 活动优惠
            val discountWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<DiscountWidgetData>(priceInfo, "discount")
            if (discountWidgetData != null) {
                PriceItem(
                    label = discountWidgetData.title ?: "",
                    value = discountWidgetData.desc ?: "",
                    valueColor = Color(0xFFFF3B30)
                )
            }

            // 包装费 配送
            val shoppingBagsDeliveryWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<ShoppingBagsDeliveryWidgetData>(
                    priceInfo,
                    "shoppingbags_delivery"
                )
            if (shoppingBagsDeliveryWidgetData != null) {
                val shoppingBagsDeliveryWidget =
                    priceInfo.data?.firstOrNull { it.name == "shoppingbags_delivery" }
                shoppingBagsDeliveryWidgetData.shoppingbags?.forEach { bag ->
                    PriceItem(
                        label = bag.name,
                        value = bag.priceNew,
                        valueColor = if (shoppingBagsDeliveryWidget?.ischecked == 1) Color(
                            0xFFFF3B30
                        ) else Color.Black
                    )
                }
            }

            // 包装费 自提
            val shoppingBagsPickSelfWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<ShoppingBagsPickSelfWidgetData>(
                    priceInfo,
                    "shoppingbags_pickself"
                )
            if (shoppingBagsPickSelfWidgetData != null) {
                val shoppingBagsPickSelfWidget =
                    priceInfo.data?.firstOrNull { it.name == "shoppingbags_pickself" }
                shoppingBagsPickSelfWidgetData.shoppingbags?.forEach { bag ->
                    PriceItem(
                        label = bag.name,
                        value = bag.priceNew,
                        valueColor = if (shoppingBagsPickSelfWidget?.ischecked == 1) Color(
                            0xFFFF3B30
                        ) else Color.Black
                    )
                }
            }

            // 优惠券
            val couponsWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(priceInfo, "coupons")
            if (couponsWidgetData != null) {
                val selectedCoupon = couponsWidgetData.selectedcoupons?.firstOrNull() ?: ""
                val selectedCouponData =
                    couponsWidgetData.availablecoupons?.firstOrNull { it.code == selectedCoupon }
                onInitOrSync(selectedCouponData, null)

                val availablecoupons = couponsWidgetData.availablecoupons ?: listOf()
                if (availablecoupons.isNotEmpty()) {
                    PriceItem(
                        label = "优惠券",
                        value = couponsWidgetData.selectedcouponsmsg
                            ?: "${availablecoupons.size}张可用",
                        valueColor = Color(0xFFFF3B30),
                        onClick = { expanded ->
                            expanded.value = !expanded.value
                        },
                        content = { expanded ->
                            // 显示可用优惠券的下拉菜单
                            if (availablecoupons.isNotEmpty()) {
                                DropdownMenu(
                                    expanded = expanded.value,
                                    onDismissRequest = { expanded.value = false }
                                ) {
                                    // 添加"不使用优惠券"选项作为第一个选项
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                text = "不使用优惠券",
                                                fontSize = 14.sp
                                            )
                                        },
                                        onClick = {
                                            // 传递空字符串表示不使用优惠券
                                            onSelectedCoupon(null)
                                            expanded.value = false
                                        },
                                        modifier = Modifier.height(36.dp)
                                    )

                                    // 显示可用优惠券列表
                                    availablecoupons.forEach { coupon ->
                                        DropdownMenuItem(
                                            text = {
                                                Column {
                                                    // 显示优惠券金额
                                                    val showDescription = coupon.showDescription
                                                    Text(
                                                        text = "$showDescription ${if (selectedCoupon == coupon.code) "已选择" else ""}",
                                                        color = Color(0xFFFF3B30),
                                                        fontSize = 14.sp
                                                    )
                                                }
                                            },
                                            onClick = {
                                                // 选择优惠券
                                                onSelectedCoupon(coupon)
                                                expanded.value = false
                                            },
                                            modifier = Modifier.height(36.dp),
                                            enabled = selectedCoupon != coupon.code
                                        )
                                    }
                                }
                            }
                        }
                    )
                }
            }

            // 红包
            val redPacketWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(priceInfo, "redpacket")
            if (redPacketWidgetData != null) {
                val selectedRedPacket = redPacketWidgetData.selectedredpackets?.firstOrNull() ?: ""
                val selectedRedPacketData =
                    redPacketWidgetData.availableredpackets?.firstOrNull { it.code == selectedRedPacket }
                onInitOrSync(null, selectedRedPacketData)

                val availableRedPackets = redPacketWidgetData.availableredpackets ?: listOf()
                if (availableRedPackets.isNotEmpty()) {
                    PriceItem(
                        label = "红包",
                        value = redPacketWidgetData.selectedredpacketsmsg
                            ?: "${availableRedPackets.size}张可用",
                        valueColor = Color(0xFFFF3B30),
                        onClick = { expanded ->
                            expanded.value = !expanded.value
                        },
                        content = { expanded ->
                            // 显示可用红包的下拉菜单
                            if (availableRedPackets.isNotEmpty()) {
                                DropdownMenu(
                                    expanded = expanded.value,
                                    onDismissRequest = { expanded.value = false }
                                ) {
                                    // 添加"不使用红包"选项作为第一个选项
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                text = "不使用红包",
                                                fontSize = 14.sp
                                            )
                                        },
                                        onClick = {
                                            // 传递空字符串表示不使用红包
                                            onSelectedRedPacket(null)
                                            expanded.value = false
                                        },
                                        modifier = Modifier.height(36.dp)
                                    )

                                    // 显示可用红包列表
                                    availableRedPackets.forEach { redPacket ->
                                        DropdownMenuItem(
                                            text = {
                                                Column {
                                                    // 显示红包金额
                                                    val amount = redPacket.amount / 100.0 // 转换为元
                                                    Text(
                                                        text = "¥${amount}元 ${if (selectedRedPacket == redPacket.code) "已选择" else ""}",
                                                        color = Color(0xFFFF3B30),
                                                        fontSize = 14.sp
                                                    )
                                                }
                                            },
                                            onClick = {
                                                // 选择红包
                                                onSelectedRedPacket(redPacket)
                                                expanded.value = false
                                            },
                                            modifier = Modifier.height(36.dp),
                                            enabled = selectedRedPacket != redPacket.code
                                        )
                                    }
                                }
                            }
                        }
                    )
                }
            }

            // 配送费
            val deliveryAmountInfoWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<DeliveryAmountWidgetData>(
                    priceInfo,
                    "deliveryamountinfo"
                )
            if (deliveryAmountInfoWidgetData != null) {
                val deliveryAmountInfoInWidgetData = deliveryAmountInfoWidgetData.deliveryamountinfo
                PriceItem(
                    label = deliveryAmountInfoWidgetData.title ?: "",
                    value = deliveryAmountInfoInWidgetData?.discountamountNew ?: "",
                    valueColor = if (deliveryAmountInfoInWidgetData?.discountamountNew != "￥0") Color(
                        0xFFFF3B30
                    ) else Color.Black
                )
            }

            // 免运券
            val freeDeliveryWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<FreeDeliveryWidgetData>(
                    priceInfo,
                    "freedelivery"
                )
            if (freeDeliveryWidgetData != null) {
                val freeDeliveryWidget =
                    priceInfo.data?.firstOrNull { it.name == "freedelivery" }
                PriceItem(
                    label = freeDeliveryWidgetData.title ?: "",
                    value = freeDeliveryWidgetData.desc ?: "",
                    valueColor = if (freeDeliveryWidget?.ischecked == 1) Color(
                        0xFFFF3B30
                    ) else Color.Black
                )
            }

            // 积分抵现
            val pointsWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<PointsWidgetData>(priceInfo, "points")
            if (pointsWidgetData != null) {
                val pointsWidget = priceInfo.data?.firstOrNull { it.name == "points" }
                val activityText = pointsWidgetData.stylizeddesc?.activitytext ?: ""
                val isAvailable = activityText != "暂无可用"

                if (isAvailable) {
                    PriceItem(
                        label = pointsWidgetData.title ?: "",
                        value = activityText,
                        valueColor = if (pointsWidget?.ischecked == 1) Color(0xFFFF3B30) else Color.Black,
                        onClick = {
                            onPointPayOption(pointsWidget?.ischecked ?: 0)
                        }
                    )
                }
            }

            // 小计/合计
            val subtotalAmountWidgetData =
                WidgetHelper.getSubWidgetDataFromGroup<SubtotalAmountWidgetData>(
                    priceInfo,
                    "subtotalamount"
                )
            if (subtotalAmountWidgetData != null) {
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 4.dp),
                    color = Color(0xFFEEEEEE)
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = subtotalAmountWidgetData.title ?: "",
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )

                    Text(
                        text = subtotalAmountWidgetData.descnew ?: "",
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp,
                        color = Color(0xFFFF3B30)
                    )
                }
            }
        }
    }
}

/**
 * 价格项
 * 显示单项价格明细的标签和值
 */
@Composable
private fun PriceItem(
    label: String,
    value: String,
    valueColor: Color = Color.Black,
    onClick: ((MutableState<Boolean>) -> Unit)? = null,
    content: @Composable ((MutableState<Boolean>) -> Unit)? = null
) {
    val expanded = remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 14.sp
        )

        Box {
            Text(
                text = value,
                fontSize = 14.sp,
                color = valueColor,
                modifier = Modifier
                    .then(
                        if (onClick != null)
                            Modifier
                                .clip(RoundedCornerShape(4.dp))
                                .clickable {
                                    onClick(expanded)
                                }
                        else
                            Modifier
                    ),
            )

            // 显示下拉内容
            content?.invoke(expanded)
        }
    }
}

/**
 * 余额支付方式部分
 * 显示余额支付方式的按钮
 */
@Composable
private fun PaymentMethodSection(orderData: OrderPlaceData, onBalancePayOption: (Int) -> Unit) {
    val combinPayData = WidgetHelper.getCombinPayData(orderData)
    if (combinPayData != null) {
        val combinPayWidgets = orderData.widgets.firstOrNull { it.name == "combinpay" }
        val balanceModel = combinPayData.balancemodel
        Card(
            colors = CardDefaults.cardColors(
                containerColor = if (combinPayWidgets?.ischecked == 1) Color(
                    0xCBF7DEF4
                ) else Color(0xCBDEE6F7)
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 4.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onBalancePayOption(combinPayWidgets?.ischecked ?: 0) }
            ) {
                Text(
                    text = (balanceModel?.title ?: "") + " " + (balanceModel?.desc ?: ""),
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    modifier = Modifier
                        .padding(8.dp)
                        .horizontalScroll(rememberScrollState())
                )
            }
        }
    }
}

/**
 * 备注部分
 * 显示订单备注信息
 */
@Composable
private fun RemarkSection(orderComment: String, onRemarkClick: (String) -> Unit) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (orderComment.isNotEmpty()) Color(
                0xCBF7DEF4
            ) else Color(0xCBDEE6F7)
        ),
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    onRemarkClick(orderComment)
                }
        ) {
            Text(
                text = if (orderComment.isNotEmpty()) "备注: $orderComment" else "订单备注",
                fontSize = 14.sp,
                lineHeight = 18.sp,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState())
                    .padding(8.dp)
            )
        }
    }
}
