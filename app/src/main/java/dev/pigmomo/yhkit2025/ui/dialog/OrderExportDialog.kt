package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.DateUtils
import dev.pigmomo.yhkit2025.utils.OrderDataCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 订单导出筛选条件
 * @param statusFilters 状态筛选列表
 * @param shopIdFilters 店铺ID筛选列表
 * @param payTypeFilters 支付方式筛选列表
 * @param productKeyword 商品关键词筛选
 * @param dateRangeFilter 日期范围筛选
 */
data class OrderExportFilter(
    val statusFilters: List<Int> = emptyList(),
    val shopIdFilters: List<String> = emptyList(),
    val payTypeFilters: List<String> = emptyList(),
    val productKeyword: String = "",
    val dateRangeFilter: DateRangeFilter? = null
)

/**
 * 日期范围筛选条件
 * @param startTimestamp 开始时间戳（毫秒）
 * @param endTimestamp 结束时间戳（毫秒）
 * @param displayName 显示名称
 */
data class DateRangeFilter(
    val startTimestamp: Long,
    val endTimestamp: Long,
    val displayName: String
)

/**
 * 订单导出对话框
 * @param onDismiss 关闭对话框回调
 * @param onConfirm 确认导出回调，传递筛选条件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OrderExportDialog(
    onDismiss: () -> Unit,
    onConfirm: (OrderExportFilter) -> Unit
) {
    // 可用的筛选条件
    val availableStatuses = remember { OrderDataCacheManager.getAvailableOrderStatuses() }
    val availableShops = remember { OrderDataCacheManager.getAvailableShops() }
    val availablePayTypes = remember { OrderDataCacheManager.getAvailablePayTypes() }
    val availableDateRanges = remember { OrderDataCacheManager.getAvailableDateRanges() }

    // 选中的筛选条件
    val selectedStatuses = remember { mutableStateListOf<Int>() }
    val selectedShops = remember { mutableStateListOf<String>() }
    val selectedPayTypes = remember { mutableStateListOf<String>() }
    var productKeyword by remember { mutableStateOf("") }
    var selectedDateRange by remember { mutableStateOf<DateRangeFilter?>(null) }

    // 计算符合条件的订单数量
    val matchingOrdersCount by remember {
        derivedStateOf {
            val filters = OrderExportFilter(
                statusFilters = selectedStatuses.toList(),
                shopIdFilters = selectedShops.toList(),
                payTypeFilters = selectedPayTypes.toList(),
                productKeyword = productKeyword,
                dateRangeFilter = selectedDateRange
            )

            // 使用现有的筛选方法计算符合条件的订单数量
            val filteredOrders = OrderDataCacheManager.filterOrdersByConditions(filters)
            filteredOrders.size
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("导出订单", fontWeight = FontWeight.Bold) },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
            ) {
                // 显示符合条件的订单数量
                Text(
                    text = "符合条件的订单数量: $matchingOrdersCount",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Spacer(modifier = Modifier.height(8.dp))

                Text("商品关键词:", fontWeight = FontWeight.Medium)
                // 商品关键词筛选
                OutlinedTextField(
                    value = productKeyword,
                    onValueChange = { productKeyword = it },
                    label = { Text("商品关键词") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )

                // 日期范围筛选
                if (availableDateRanges.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))

                    Text("日期范围:", fontWeight = FontWeight.Medium)
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        availableDateRanges.forEach { dateRange ->
                            FilterChip(
                                selected = selectedDateRange == dateRange,
                                onClick = {
                                    selectedDateRange = if (selectedDateRange == dateRange) {
                                        null
                                    } else {
                                        dateRange
                                    }
                                },
                                label = { Text(dateRange.displayName) },
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = CardContainerColor
                                )
                            )
                        }
                    }
                }

                // 订单状态筛选标题
                if (availableStatuses.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))

                    Text("订单状态:", fontWeight = FontWeight.Medium)
                    // 订单状态筛选按钮
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        availableStatuses.forEach { status ->
                            FilterChip(
                                selected = selectedStatuses.contains(status.first),
                                onClick = {
                                    if (selectedStatuses.contains(status.first)) {
                                        selectedStatuses.remove(status.first)
                                    } else {
                                        selectedStatuses.add(status.first)
                                    }
                                },
                                label = { Text(status.second) },
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = CardContainerColor
                                )
                            )
                        }
                    }
                }
                
                if (availableShops.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 店铺筛选标题
                    Text("店铺:", fontWeight = FontWeight.Medium)

                    // 店铺筛选按钮
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        availableShops.forEach { shop ->
                            FilterChip(
                                selected = selectedShops.contains(shop.first),
                                onClick = {
                                    if (selectedShops.contains(shop.first)) {
                                        selectedShops.remove(shop.first)
                                    } else {
                                        selectedShops.add(shop.first)
                                    }
                                },
                                label = { Text(shop.second) },
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = CardContainerColor
                                )
                            )
                        }
                    }
                }
                
                if (availablePayTypes.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))

                    // 支付方式筛选标题
                    Text("支付方式:", fontWeight = FontWeight.Medium)

                    // 支付方式筛选按钮
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        availablePayTypes.forEach { payType ->
                            FilterChip(
                                selected = selectedPayTypes.contains(payType),
                                onClick = {
                                    if (selectedPayTypes.contains(payType)) {
                                        selectedPayTypes.remove(payType)
                                    } else {
                                        selectedPayTypes.add(payType)
                                    }
                                },
                                label = { Text(payType) },
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = CardContainerColor
                                )
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    // 创建筛选条件对象
                    val filters = OrderExportFilter(
                        statusFilters = selectedStatuses.toList(),
                        shopIdFilters = selectedShops.toList(),
                        payTypeFilters = selectedPayTypes.toList(),
                        productKeyword = productKeyword,
                        dateRangeFilter = selectedDateRange
                    )

                    onConfirm(filters)
                },
                enabled = matchingOrdersCount > 0
            ) {
                Text("导出 ($matchingOrdersCount)")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
} 