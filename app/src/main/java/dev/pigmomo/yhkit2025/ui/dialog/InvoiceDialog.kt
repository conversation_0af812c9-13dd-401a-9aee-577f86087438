package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import dev.pigmomo.yhkit2025.api.model.order.InvoiceCanApplyOrder
import dev.pigmomo.yhkit2025.api.model.order.InvoiceCanApplyOrderItem
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 发票可申请订单列表对话框
 *
 * @param onDismiss 关闭对话框回调
 * @param orderList 可申请发票的订单列表
 * @param page 当前页码
 * @param pageCount 总页数
 * @param onLoadMore 加载更多数据回调
 * @param onSetInvoiceOrderId 申请发票回调
 */
@Composable
fun InvoiceCanApplyOrderListDialog(
    onDismiss: () -> Unit,
    orderList: List<InvoiceCanApplyOrder>,
    page: Int,
    pageCount: Int,
    onLoadMore: (Int) -> Unit,
    onSetInvoiceOrderId: (String) -> Unit
) {
    val listState = rememberLazyListState()

    // 检测是否滚动到底部，如果是则加载更多
    val shouldLoadMore = remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            val totalItemsNumber = layoutInfo.totalItemsCount
            val lastVisibleItemIndex = (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1

            lastVisibleItemIndex > 0 && lastVisibleItemIndex >= totalItemsNumber - 2 && page < pageCount - 1
        }
    }

    // 当滚动到底部时加载更多
    LaunchedEffect(shouldLoadMore.value) {
        if (shouldLoadMore.value) {
            val nextPage = page + 1
            if (nextPage < pageCount) {
                onLoadMore(nextPage)
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text("请选择可申请发票的记录")
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .heightIn(max = 300.dp)
            ) {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(orderList) { order ->
                        InvoiceCanApplyOrderItem(
                            order = order,
                            onSetInvoiceOrderId = onSetInvoiceOrderId
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    item {
                        if (!shouldLoadMore.value) {
                            Text(
                                text = "到底啦！！！",
                                fontSize = 10.sp,
                                lineHeight = 12.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {},
        containerColor = dialogContainerColor()
    )
}

/**
 * 发票可申请订单项
 *
 * @param order 可申请发票的订单
 * @param onSetInvoiceOrderId 申请发票回调
 */
@Composable
fun InvoiceCanApplyOrderItem(
    order: InvoiceCanApplyOrder,
    onSetInvoiceOrderId: (String) -> Unit
) {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    val formattedDate = dateFormat.format(Date(order.createdat))

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay(),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .clickable {
                    onSetInvoiceOrderId(order.orderid)
                },
        ) {
            // 订单头部信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp, end = 12.dp, top = 12.dp)
            ) {
                Text(
                    text = order.shopfullname,
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp,
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = order.ordertypetag,
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 订单ID和时间
            Text(
                text = "订单号: ${order.orderid}",
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(start = 12.dp)
            )

            Text(
                text = "下单时间: $formattedDate",
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(start = 12.dp)
            )

            // 商品列表
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(start = 12.dp, end = 12.dp, bottom = 12.dp)
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState())
            ) {
                order.items.forEach { item ->
                    OrderItem(item)
                }
            }
        }
    }
}

/**
 * 订单商品行
 *
 * @param item 订单商品项
 */
@Composable
fun OrderItem(item: InvoiceCanApplyOrderItem) {
    Box(modifier = Modifier.padding(end = 4.dp)) {
        // 商品图片
        AsyncImage(
            model = item.mainimg,
            contentDescription = item.goodsname,
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(4.dp)),
            contentScale = ContentScale.Crop
        )

        // 数量标签
        Text(
            text = " x${item.qty.toInt()} ",
            color = Color.White,
            fontSize = 10.sp,
            lineHeight = 10.sp,
            modifier = Modifier
                .offset(x = 2.dp, y = (-2).dp)
                .align(Alignment.TopEnd)
                .background(
                    color = Color(0x80000000),
                    shape = RoundedCornerShape(6.dp)
                )
        )
    }
} 