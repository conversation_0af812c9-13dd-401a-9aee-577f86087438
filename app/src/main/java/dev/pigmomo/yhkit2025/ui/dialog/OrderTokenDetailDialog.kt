package dev.pigmomo.yhkit2025.ui.dialog

import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.ui.components.LogFilterDropdown
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import dev.pigmomo.yhkit2025.utils.ClipboardUtils

@Composable
fun OrderTokenDetailDialog(
    token: OrderTokenEntity,
    viewModel: OrderViewModel,
    onDismiss: () -> Unit
) {
    // 本地状态管理
    val logs = remember { mutableStateOf<List<LogEntity>>(emptyList()) }
    val isLoadingLogs = remember { mutableStateOf(false) }
    val pageOffset = remember { mutableIntStateOf(0) }
    val pageSize = remember { mutableIntStateOf(10) }
    var selectedFilter by remember { mutableStateOf("全部") }

    val lazyListState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 检测是否滚动到底部以加载更多
    val isAtBottom by remember {
        derivedStateOf {
            val layoutInfo = lazyListState.layoutInfo
            val visibleItemsInfo = layoutInfo.visibleItemsInfo
            if (visibleItemsInfo.isEmpty()) {
                false
            } else {
                val lastVisibleItem = visibleItemsInfo.last()
                lastVisibleItem.index >= layoutInfo.totalItemsCount - 1
            }
        }
    }

    // 加载日志函数
    val loadLogs = { refresh: Boolean ->
        coroutineScope.launch {
            if (refresh) {
                pageOffset.intValue = 0
            }

            isLoadingLogs.value = true
            try {
                val newLogs = viewModel.getLogsByToken(
                    tokenUid = token.uid,
                    logLevel = if (selectedFilter == "全部") null else selectedFilter,
                    offset = pageOffset.intValue,
                    limit = pageSize.intValue
                )

                if (refresh) {
                    logs.value = newLogs
                    pageOffset.intValue = newLogs.size
                } else {
                    logs.value = logs.value + newLogs
                    pageOffset.intValue += newLogs.size
                }
            } catch (e: Exception) {
                // 处理错误
            } finally {
                isLoadingLogs.value = false
            }
        }
    }

    // 当滚动到底部时加载更多
    LaunchedEffect(isAtBottom) {
        if (isAtBottom && logs.value.isNotEmpty() && !isLoadingLogs.value) {
            loadLogs(false)
        }
    }

    // 初始加载日志和筛选器变化时重新加载
    LaunchedEffect(token.uid, selectedFilter) {
        loadLogs(true)
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("账号信息") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.horizontalScroll(rememberScrollState())
                    ) {
                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制账号",
                            onClick = { viewModel.copyTokenToClipboard(token) }
                        )

                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制手机号",
                            onClick = { viewModel.copyPhoneNumberToClipboard(token) }
                        )

                        TokenActionButton(
                            icon = R.drawable.baseline_notes_24,
                            text = "复制备注",
                            onClick = { viewModel.copyExtraNoteToClipboard(token) }
                        )

                        // 设定按钮
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            text = "设定",
                            onClick = {
                                viewModel.saveOrderTokenToPrefs(token)
                            }
                        )

                        // 移至最后
                        TokenActionButton(
                            icon = R.drawable.outline_move_down_24,
                            text = "移至最后",
                            onClick = {
                                viewModel.moveOrderTokenToLast(token)
                                onDismiss() // 关闭对话框，以便看到移动效果
                            }
                        )

                        // 撤回，只有才新移动的账号可以撤回
                        val lastMovedToken by viewModel.lastMovedToken
                        val canRevoke = lastMovedToken != null && lastMovedToken?.uid == token.uid
                        if (canRevoke) {
                            TokenActionButton(
                                icon = R.drawable.outline_undo_24,
                                text = "撤回",
                                onClick = {
                                    viewModel.revokeOrderToken(token)
                                    onDismiss() // 关闭对话框，以便看到撤回效果
                                }
                            )
                        }

                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 日志标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "日志记录(${logs.value.size})：",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    val filterOptions = listOf(
                        "全部" to Icons.Filled.Home,
                        "INFO" to Icons.Filled.Info,
                        "WARNING" to Icons.Filled.Warning,
                        "ERROR" to Icons.Filled.Close
                    )

                    LogFilterDropdown(
                        selectedFilter = selectedFilter,
                        onFilterSelected = { selectedFilter = it },
                        filterOptions = filterOptions
                    )
                }

                // 日志列表
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                ) {
                    Box(modifier = Modifier.fillMaxWidth()) {
                        LazyColumn(
                            state = lazyListState,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                        ) {
                            items(logs.value) { log ->
                                LogItem(log)
                                HorizontalDivider(
                                    modifier = Modifier.padding(vertical = 4.dp),
                                    color = MaterialTheme.colorScheme.surfaceVariant
                                )
                            }

                            if (logs.value.isEmpty() && !isLoadingLogs.value) {
                                item {
                                    Text(
                                        text = "暂无日志记录",
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp),
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }

                        // 加载指示器
                        if (isLoadingLogs.value) {
                            CircularProgressIndicator(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .padding(16.dp)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = { },
        dismissButton = null
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun LogItem(log: LogEntity) {
    val context = LocalContext.current

    val dateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
    ) {
        // 日志时间和标签
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(2f)
            ) {
                // 日志级别标签
                Box(
                    modifier = Modifier
                        .background(
                            when (log.logLevel) {
                                "ERROR" -> MaterialTheme.colorScheme.errorContainer
                                "WARNING" -> MaterialTheme.colorScheme.secondaryContainer
                                else -> MaterialTheme.colorScheme.primaryContainer
                            },
                            shape = MaterialTheme.shapes.small
                        )
                        .padding(horizontal = 4.dp, vertical = 1.dp)
                ) {
                    Text(
                        text = log.logLevel,
                        fontSize = 11.sp,
                        color = when (log.logLevel) {
                            "ERROR" -> MaterialTheme.colorScheme.onErrorContainer
                            "WARNING" -> MaterialTheme.colorScheme.onSecondaryContainer
                            else -> MaterialTheme.colorScheme.onPrimaryContainer
                        }
                    )
                }

                Text(
                    text = dateFormat.format(log.timestamp),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(start = 2.dp).horizontalScroll(rememberScrollState())
                )
            }

            // 日志标签
            Text(
                text = log.tag,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.weight(1f).horizontalScroll(rememberScrollState()),
                textAlign = TextAlign.End
            )
        }

        // 日志内容
        Text(
            text = log.message,
            maxLines = 1,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier
                .padding(top = 4.dp)
                .clip(RoundedCornerShape(4.dp))
                .clickable {
                    ClipboardUtils.copyTextToClipboard(context, log.message)
                    Toast.makeText(context, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
                }
                .horizontalScroll(rememberScrollState()),
            overflow = TextOverflow.Ellipsis
        )
    }
}