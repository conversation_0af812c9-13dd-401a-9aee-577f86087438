package dev.pigmomo.yhkit2025.viewmodel

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailProduct
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailPriceDetail
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailTimeSlot
import dev.pigmomo.yhkit2025.api.model.order.Button
import dev.pigmomo.yhkit2025.api.model.order.InvoiceDetail

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 订单详情ViewModel
 * 负责管理订单详情页面的数据和业务逻辑
 */
class OrderDetailViewModel(
    application: Application
) : AndroidViewModel(application) {

    @SuppressLint("StaticFieldLeak")
    private val context: Context = getApplication()

    // 订单详情数据
    private val _orderDetailData = MutableStateFlow<OrderDetailData?>(null)
    val orderDetailData: StateFlow<OrderDetailData?> = _orderDetailData.asStateFlow()

    // 加载状态
    private val _isLoading = mutableStateOf(false)
    val isLoading: State<Boolean> = _isLoading

    // 错误消息
    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: State<String?> = _errorMessage

    // Toast消息
    private val _toastMessage = MutableStateFlow<String?>(null)
    val toastMessage: StateFlow<String?> = _toastMessage.asStateFlow()

    /**
     * 设置订单详情数据
     * @param orderDetail 订单详情数据
     */
    fun setOrderDetailData(orderDetail: OrderDetailData) {
        _orderDetailData.value = orderDetail
        _errorMessage.value = null
        Log.d("OrderDetailViewModel", "Order detail data set: ${orderDetail.baseinfo?.id}")
    }

    /**
     * 获取订单状态文本
     */
    fun getOrderStatusText(): String {
        return _orderDetailData.value?.statusmsg ?: "未知状态"
    }

    /**
     * 获取订单状态标题
     */
    fun getOrderStatusTitle(): String {
        return _orderDetailData.value?.statusinfo?.title ?: ""
    }

    /**
     * 获取店铺名称
     */
    fun getShopName(): String {
        return _orderDetailData.value?.shopinfo?.seller?.title ?: "未知店铺"
    }

    /**
     * 获取店铺图标
     */
    fun getShopIcon(): String {
        return _orderDetailData.value?.shopinfo?.shopicon ?: ""
    }

    /**
     * 获取商品列表
     */
    fun getProductList(): List<OrderDetailProduct> {
        return _orderDetailData.value?.productsinfo?.products ?: emptyList()
    }

    /**
     * 获取价格详情列表
     */
    fun getPriceDetailList(): List<OrderDetailPriceDetail> {
        return _orderDetailData.value?.pricedetail ?: emptyList()
    }

    /**
     * 获取实付金额
     */
    fun getTotalPayment(): String {
        val totalPayment = _orderDetailData.value?.orderpayinfo?.totalpayment ?: 0
        return formatPrice(totalPayment)
    }

    /**
     * 获取总优惠金额
     */
    fun getTotalDiscount(): String {
        val totalDiscount = _orderDetailData.value?.orderpayinfo?.totaldiscount ?: 0
        return formatPrice(totalDiscount)
    }

    /**
     * 获取订单编号
     */
    fun getOrderId(): String {
        return _orderDetailData.value?.baseinfo?.id ?: ""
    }

    /**
     * 获取下单时间
     */
    fun getOrderTime(): String {
        val generateTime = _orderDetailData.value?.baseinfo?.generateTime ?: 0
        return if (generateTime > 0) {
            formatTimestamp(generateTime)
        } else {
            "未知时间"
        }
    }

    /**
     * 获取支付方式
     */
    fun getPaymentMethod(): String {
        return _orderDetailData.value?.baseinfo?.paytypes ?: "未支付"
    }

    /**
     * 获取收货地址
     */
    fun getDeliveryAddress(): String {
        val recvInfo = _orderDetailData.value?.commentinfo?.recvinfo
        return if (recvInfo != null) {
            val area = recvInfo.address?.area ?: ""
            val detail = recvInfo.address?.detail ?: ""
            "$area$detail"
        } else {
            "未知地址"
        }
    }

    /**
     * 获取收货人信息
     */
    fun getReceiverInfo(): String {
        val recvInfo = _orderDetailData.value?.commentinfo?.recvinfo
        return if (recvInfo != null) {
            "${recvInfo.name} ${recvInfo.phone}"
        } else {
            "未知收货人"
        }
    }

    /**
     * 获取预约时间
     */
    fun getExpectedTime(): String {
        return _orderDetailData.value?.commentinfo?.expecttime ?: "尽快送达"
    }

    /**
     * 获取是否显示时间线
     */
    fun getShowTimeslots(): Boolean {
        val timeslots = _orderDetailData.value?.timeslots
        return timeslots?.show ?: false
    }

    /**
     * 获取订单备注
     */
    fun getOrderComment(): String {
        return _orderDetailData.value?.commentinfo?.comment ?: ""
    }

    /**
     * 获取缺货信息
     */
    fun getOutOfStockMessage(): String {
        return _orderDetailData.value?.baseinfo?.outOfStockMsg ?: ""
    }

    /**
     * 获取状态按钮列表
     */
    fun getStatusButtons(): List<Button> {
        return _orderDetailData.value?.statusinfo?.buttons ?: emptyList()
    }

    /**
     * 获取送达照片列表
     */
    fun getDeliveryPhotos(): List<String> {
        return _orderDetailData.value?.noTouchPics?.pics ?: emptyList()
    }

    /**
     * 获取送达照片标题
     */
    fun getDeliveryPhotosTitle(): String {
        return _orderDetailData.value?.noTouchPics?.title ?: "您的商品已放在约定处，请及时提取"
    }

    /**
     * 获取配送员信息
     */
    fun getCarrierInfo(): String {
        val commentInfo = _orderDetailData.value?.commentinfo
        return if (commentInfo != null && commentInfo.carriername.isNotEmpty()) {
            "${commentInfo.carriername} ${commentInfo.carrierphone}"
        } else {
            ""
        }
    }

    /**
     * 获取发票信息
     */
    fun getInvoiceInfo(): String {
        val invoiceDetail = _orderDetailData.value?.commentinfo?.invoicedetail
        return if (invoiceDetail != null && invoiceDetail.showflag == 1) {
            invoiceDetail.patchstatusdesc
        } else {
            ""
        }
    }

    /**
     * 删除订单
     */
    fun deleteOrder() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现删除订单的API调用
                showToast("删除订单功能待实现")
            } catch (e: Exception) {
                Log.e("OrderDetailViewModel", "Delete order failed", e)
                showToast("删除订单失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 再来一单
     */
    fun reorder() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现再来一单的逻辑，将商品添加到购物车
                showToast("再来一单功能待实现")
            } catch (e: Exception) {
                Log.e("OrderDetailViewModel", "Reorder failed", e)
                showToast("再来一单失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 联系客服
     */
    fun contactCustomerService() {
        showToast("联系客服功能待实现")
    }

    /**
     * 查看物流信息
     */
    fun viewDeliveryInfo() {
        showToast("查看物流信息功能待实现")
    }

    /**
     * 查看送达照片
     */
    fun viewDeliveryPhotos() {
        val photos = getDeliveryPhotos()
        if (photos.isNotEmpty()) {
            showToast("查看送达照片功能待实现")
            // TODO: 实现照片查看功能
        } else {
            showToast("暂无送达照片")
        }
    }

    /**
     * 打开发票
     */
    fun openInvoice() {
        val invoiceDetail = _orderDetailData.value?.commentinfo?.invoicedetail
        if (invoiceDetail != null && invoiceDetail.actionUrl.isNotEmpty()) {
            showToast("打开发票功能待实现")
            // TODO: 实现打开发票页面
        } else {
            showToast("暂无发票信息")
        }
    }

    /**
     * 显示Toast消息
     */
    fun showToast(message: String) {
        viewModelScope.launch {
            _toastMessage.value = message
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 清除Toast消息
     */
    fun clearToastMessage() {
        _toastMessage.value = null
    }

    /**
     * 格式化价格 (分转元)
     */
    @SuppressLint("DefaultLocale")
    private fun formatPrice(priceInCents: Int): String {
        return String.format("%.2f", priceInCents / 100.0)
    }

    /**
     * 格式化时间戳
     */
    private fun formatTimestamp(timestamp: Long): String {
        return try {
            val date = Date(timestamp) // 转换为毫秒
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }

    /**
     * ViewModel工厂类
     */
    class Factory(
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(OrderDetailViewModel::class.java)) {
                return OrderDetailViewModel(application) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
