package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import android.widget.Toast
import dev.pigmomo.yhkit2025.ui.dialog.DateRangeFilter
import dev.pigmomo.yhkit2025.ui.dialog.OrderExportFilter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.concurrent.ConcurrentHashMap

/**
 * 订单数据缓存管理器
 * 用于在内存中存储订单数据，不使用数据库
 */
object OrderDataCacheManager {
    private const val TAG = "OrderDataCacheManager"

    // 默认的导出文件子目录名称
    private const val DEFAULT_EXPORT_SUBDIRECTORY = "OrderExport"

    // 使用ConcurrentHashMap保证线程安全
    private val orderDataMap = ConcurrentHashMap<String, List<OrderData>>()

    /**
     * 保存指定手机号的订单数据
     * @param phoneNumber 手机号
     * @param orderDataList 订单数据列表
     */
    fun saveOrderData(phoneNumber: String, orderDataList: List<OrderData>) {
        orderDataMap[phoneNumber] = orderDataList
    }

    /**
     * 获取所有手机号的订单数据
     * @return 所有订单数据的Map，键为手机号，值为订单数据列表
     */
    fun getAllOrderData(): Map<String, List<OrderData>> = orderDataMap.toMap()

    /**
     * 获取所有订单数据列表（扁平化）
     * @return 所有订单列表
     */
    fun getAllOrdersList(): List<OrderData> = orderDataMap.values.flatten()

    /**
     * 清除指定手机号的订单数据
     * @param phoneNumber 手机号
     */
    fun clearOrderData(phoneNumber: String) {
        orderDataMap.remove(phoneNumber)
    }

    /**
     * 清除所有订单数据
     */
    fun clearAllOrderData() {
        orderDataMap.clear()
    }

    /**
     * 获取所有订单数据的总数
     * @return 所有订单的总数
     */
    fun getTotalOrderCount(): Int = orderDataMap.values.sumOf { it.size }

    /**
     * 通用筛选方法
     * @param predicate 筛选条件
     * @return 符合条件的订单列表
     */
    fun filterOrders(predicate: (OrderData) -> Boolean): List<OrderData> =
        getAllOrdersList().filter(predicate)

    /**
     * 根据筛选条件筛选订单
     * @param filters 筛选条件
     * @return 符合条件的订单列表
     */
    fun filterOrdersByConditions(filters: OrderExportFilter): List<OrderData> {
        return getAllOrdersList().filter { order ->
            // 应用所有筛选条件，如果任一条件不符合则排除
            (filters.statusFilters.isEmpty() || filters.statusFilters.contains(order.status)) &&
                    (filters.shopIdFilters.isEmpty() || filters.shopIdFilters.contains(order.shopId)) &&
                    (filters.payTypeFilters.isEmpty() || filters.payTypeFilters.any { payType ->
                        order.payType.contains(
                            payType
                        )
                    }) &&
                    (filters.productKeyword.isEmpty() || order.productInfo.contains(filters.productKeyword)) &&
                    (filters.dateRangeFilter == null || isOrderInDateRange(order, filters.dateRangeFilter))
        }
    }

    /**
     * 检查订单是否在指定日期范围内
     * @param order 订单数据
     * @param dateRange 日期范围
     * @return 是否在范围内
     */
    private fun isOrderInDateRange(order: OrderData, dateRange: DateRangeFilter): Boolean {
        if (order.payTime.isEmpty()) return false

        return try {
            val orderTimestamp = DateUtils.parseDateTime(order.payTime)
            orderTimestamp in dateRange.startTimestamp..dateRange.endTimestamp
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 导出订单数据为CSV格式字符串
     * @param orders 要导出的订单列表
     * @return CSV格式的字符串
     */
    fun exportOrdersToCSV(orders: List<OrderData>): String {
        val header =
            "手机号,订单ID,状态,状态描述,支付金额,支付方式,店铺ID,店铺名称,商品信息,支付时间,完成时间,配送方式"

        val rows = orders.map { order ->
            with(order) {
                "$phoneNumber,$orderId,$status,$statusMsg,$payAmount," +
                        "$payType,$shopId,$shopName,\"${productInfo.replace("\"", "\"\"")}\"," +
                        "$payTime,$completeTime,$deliveryMode"
            }
        }

        return (listOf(header) + rows).joinToString("\n")
    }

    /**
     * 将订单数据导出到文件
     * @param context 上下文
     * @param orders 要导出的订单列表
     * @param fileNamePrefix 文件名前缀
     * @param customSubDirectory 自定义子目录名称，为null时使用默认子目录
     * @return 导出的文件路径，如果导出失败则返回null
     */
    private fun exportOrdersToFile(
        context: Context,
        orders: List<OrderData>,
        fileNamePrefix: String,
        customSubDirectory: String? = null
    ): String? {
        if (orders.isEmpty()) {
            Log.e(TAG, "No orders to export")
            return null
        }

        return try {
            // 生成CSV内容
            val csvContent = exportOrdersToCSV(orders)

            // 创建文件名（包含时间戳）
            val timestamp = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyyMMdd_HHmmss")
            val fileName = "${fileNamePrefix}_$timestamp.csv"

            // 确定使用的子目录名称
            val subDirectory = customSubDirectory ?: DEFAULT_EXPORT_SUBDIRECTORY

            // 尝试保存到公共下载目录的子目录，如果失败则保存到应用私有目录的子目录
            val filePath = FileUtils.saveTextToPublicDownloads(context, fileName, csvContent, subDirectory)
                ?: FileUtils.saveTextToDownloads(context, fileName, csvContent, subDirectory)
            Log.d(TAG, "Orders exported to file: $filePath")
            filePath
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting orders to file", e)
            null
        }
    }

    /**
     * 导出订单功能
     * @param context 上下文
     * @param filters 订单导出筛选条件
     * @param customSubDirectory 自定义子目录名称，为null时使用默认子目录
     */
    fun exportOrders(context: Context, filters: OrderExportFilter, customSubDirectory: String? = null) {
        // 使用协程在后台线程执行导出操作
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 获取符合筛选条件的订单
                val ordersToExport = filterOrdersByConditions(filters)

                // 文件名前缀，使用不为空的筛选条件名生成
                val fileNamePrefix = "orders_filtered" + when {
                    filters.statusFilters.isNotEmpty() -> "_status"
                    filters.shopIdFilters.isNotEmpty() -> "_shop"
                    filters.payTypeFilters.isNotEmpty() -> "_pay"
                    filters.productKeyword.isNotBlank() -> "_product"
                    else -> ""
                }

                // 导出订单
                val filePath = exportOrdersToFile(context, ordersToExport, fileNamePrefix, customSubDirectory)

                // 在主线程显示结果
                withContext(Dispatchers.Main) {
                    if (filePath != null) {
                        Toast.makeText(context, "已导出成功", Toast.LENGTH_LONG).show()
                        // 复制到剪贴板
                        ClipboardUtils.copyTextToClipboard(context, filePath)
                        Toast.makeText(context, "导出路径已复制到剪贴板", Toast.LENGTH_SHORT).show()
                        Log.d(TAG, "Orders exported successfully to: $filePath")
                    } else {
                        Toast.makeText(context, "没有符合条件的订单可导出", Toast.LENGTH_SHORT)
                            .show()
                        Log.d(TAG, "No orders to export that match the filters")
                    }
                }
            } catch (e: Exception) {
                // 在主线程显示错误
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "导出订单错误: ${e.message}", Toast.LENGTH_SHORT).show()
                    Log.e(TAG, "Failed to export orders", e)
                }
            }
        }
    }

    /**
     * 获取数据统计信息
     * @return 包含各种统计信息的Map
     */
    fun getStatistics(): Map<String, Any> {
        val orders = getAllOrdersList()
        if (orders.isEmpty()) return emptyMap()

        return mapOf(
            "totalOrders" to orders.size,
            "totalAmount" to orders.sumOf { it.payAmount },
            "availableStatuses" to getAvailableOrderStatuses(),
            "availableShops" to getAvailableShops(),
            "availablePayTypes" to getAvailablePayTypes(),
            "phoneNumbers" to getAvailablePhoneNumbers(),
            "amountRange" to getOrderAmountRange(),
            "timeRange" to getOrderTimeRange()
        )
    }

    /**
     * 获取所有可用的订单状态
     * @return 订单状态列表，包含状态码和状态描述
     */
    fun getAvailableOrderStatuses(): List<Pair<Int, String>> {
        return getAllOrdersList()
            .distinctBy { it.status }
            .map { Pair(it.status, it.statusMsg) }
            .sortedBy { it.first }
    }

    /**
     * 获取所有可用的店铺ID和名称
     * @return 店铺ID和名称列表
     */
    fun getAvailableShops(): List<Pair<String, String>> {
        return getAllOrdersList()
            .filter { it.shopId.isNotEmpty() }
            .distinctBy { it.shopId }
            .map { Pair(it.shopId, it.shopName) }
            .sortedBy { it.second }
    }

    /**
     * 获取所有可用的支付方式
     * @return 支付方式列表
     */
    fun getAvailablePayTypes(): List<String> {
        return getAllOrdersList()
            .filter { it.payType.isNotEmpty() }
            .map { it.payType }
            .distinct()
            .sorted()
    }

    /**
     * 获取所有可用的手机号
     * @return 手机号列表
     */
    fun getAvailablePhoneNumbers(): List<String> = orderDataMap.keys.toList().sorted()

    /**
     * 获取订单金额范围
     * @return Pair(最小金额, 最大金额)
     */
    fun getOrderAmountRange(): Pair<Double, Double> {
        val orders = getAllOrdersList()
        if (orders.isEmpty()) return Pair(0.0, 0.0)

        return Pair(
            orders.minOfOrNull { it.payAmount } ?: 0.0,
            orders.maxOfOrNull { it.payAmount } ?: 0.0
        )
    }

    /**
     * 获取订单支付时间范围
     * @return Pair(最早时间戳, 最晚时间戳)
     */
    fun getOrderTimeRange(): Pair<Long, Long> {
        val orders = getAllOrdersList()
        if (orders.isEmpty()) return Pair(0L, System.currentTimeMillis())

        val validTimes = orders
            .mapNotNull { order ->
                if (order.payTime.isNotEmpty()) {
                    try {
                        DateUtils.parseDateTime(order.payTime)
                    } catch (e: Exception) {
                        null
                    }
                } else null
            }
            .filter { it > 0 }

        return if (validTimes.isEmpty()) {
            Pair(0L, System.currentTimeMillis())
        } else {
            Pair(validTimes.minOrNull() ?: 0L, validTimes.maxOrNull() ?: System.currentTimeMillis())
        }
    }

    /**
     * 获取可选的日期范围列表
     * @return 日期范围列表
     */
    fun getAvailableDateRanges(): List<DateRangeFilter> {
        val orders = getAllOrdersList()
        if (orders.isEmpty()) return emptyList()

        val validTimes = orders
            .mapNotNull { order ->
                if (order.payTime.isNotEmpty()) {
                    try {
                        DateUtils.parseDateTime(order.payTime)
                    } catch (e: Exception) {
                        null
                    }
                } else null
            }
            .filter { it > 0 }
            .sorted()

        if (validTimes.isEmpty()) return emptyList()

        val ranges = mutableListOf<DateRangeFilter>()
        val now = System.currentTimeMillis()
        val calendar = Calendar.getInstance()

        // 今天
        calendar.timeInMillis = now
        val todayStart = getStartOfDay(calendar.timeInMillis)
        val todayEnd = getEndOfDay(calendar.timeInMillis)
        if (validTimes.any { it in todayStart..todayEnd }) {
            ranges.add(DateRangeFilter(todayStart, todayEnd, "今天"))
        }

        // 昨天
        calendar.add(Calendar.DAY_OF_MONTH, -1)
        val yesterdayStart = getStartOfDay(calendar.timeInMillis)
        val yesterdayEnd = getEndOfDay(calendar.timeInMillis)
        if (validTimes.any { it in yesterdayStart..yesterdayEnd }) {
            ranges.add(DateRangeFilter(yesterdayStart, yesterdayEnd, "昨天"))
        }

        // 最近7天
        calendar.timeInMillis = now
        calendar.add(Calendar.DAY_OF_MONTH, -6)
        val last7DaysStart = getStartOfDay(calendar.timeInMillis)
        if (validTimes.any { it in last7DaysStart..todayEnd }) {
            ranges.add(DateRangeFilter(last7DaysStart, todayEnd, "最近7天"))
        }

        // 最近30天
        calendar.timeInMillis = now
        calendar.add(Calendar.DAY_OF_MONTH, -29)
        val last30DaysStart = getStartOfDay(calendar.timeInMillis)
        if (validTimes.any { it in last30DaysStart..todayEnd }) {
            ranges.add(DateRangeFilter(last30DaysStart, todayEnd, "最近30天"))
        }

        // 按月份分组
        val monthlyRanges = validTimes
            .groupBy { timestamp ->
                calendar.timeInMillis = timestamp
                "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}"
            }
            .map { (monthKey, timestamps) ->
                val parts = monthKey.split("-")
                val year = parts[0].toInt()
                val month = parts[1].toInt() - 1 // Calendar.MONTH is 0-based

                calendar.set(year, month, 1, 0, 0, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                val monthStart = calendar.timeInMillis

                calendar.set(year, month, calendar.getActualMaximum(Calendar.DAY_OF_MONTH), 23, 59, 59)
                calendar.set(Calendar.MILLISECOND, 999)
                val monthEnd = calendar.timeInMillis

                val displayName = "${year}年${month + 1}月"
                DateRangeFilter(monthStart, monthEnd, displayName)
            }
            .sortedByDescending { it.startTimestamp }

        ranges.addAll(monthlyRanges)

        // 全部时间
        ranges.add(DateRangeFilter(validTimes.first(), validTimes.last(), "全部时间"))

        return ranges.distinctBy { "${it.startTimestamp}-${it.endTimestamp}" }
    }

    /**
     * 导出订单到指定子目录
     * @param context 上下文
     * @param filters 订单导出筛选条件
     * @param subDirectory 子目录名称
     */
    fun exportOrdersToSubDirectory(context: Context, filters: OrderExportFilter, subDirectory: String) {
        exportOrders(context, filters, subDirectory)
    }

    /**
     * 导出订单到按日期命名的子目录
     * @param context 上下文
     * @param filters 订单导出筛选条件
     */
    fun exportOrdersToDateSubDirectory(context: Context, filters: OrderExportFilter) {
        val dateSubDirectory = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyy-MM")
        exportOrders(context, filters, "$DEFAULT_EXPORT_SUBDIRECTORY/$dateSubDirectory")
    }

    /**
     * 导出订单到按筛选条件命名的子目录
     * @param context 上下文
     * @param filters 订单导出筛选条件
     */
    fun exportOrdersToFilterSubDirectory(context: Context, filters: OrderExportFilter) {
        val filterSubDirectory = when {
            filters.statusFilters.isNotEmpty() -> "Status"
            filters.shopIdFilters.isNotEmpty() -> "Shop"
            filters.payTypeFilters.isNotEmpty() -> "PayType"
            filters.productKeyword.isNotBlank() -> "Product"
            else -> "All"
        }
        exportOrders(context, filters, "$DEFAULT_EXPORT_SUBDIRECTORY/$filterSubDirectory")
    }

    /**
     * 获取一天的开始时间戳（00:00:00.000）
     */
    private fun getStartOfDay(timestamp: Long): Long {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    /**
     * 获取一天的结束时间戳（23:59:59.999）
     */
    private fun getEndOfDay(timestamp: Long): Long {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.timeInMillis
    }
}

/**
 * 订单数据类，用于存储订单关键信息
 */
data class OrderData(
    val phoneNumber: String,
    val orderId: String,
    val status: Int,
    val statusMsg: String,
    val payAmount: Double,
    val payType: String,
    val shopId: String,
    val shopName: String,
    val productInfo: String,
    val payTime: String,
    val completeTime: String,
    val deliveryMode: String,
)